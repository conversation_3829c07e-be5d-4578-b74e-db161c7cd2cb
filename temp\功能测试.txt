CREATE DEFINER=`root`@`%` PROCEDURE `rms_fx_cgl`(
    IN p_Code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_yp_tj VARCHAR(20),
    IN p_dcsl VARCHAR(20),
    IN p_gydw VARCHAR(20),
    IN p_gypc VARCHAR(20),
    IN p_csrq DATE,
    IN p_tz VARCHAR(20)
)
    COMMENT '单次常规用量分析存储过程'
main_block: BEGIN
	DECLARE v_sda_id VARCHAR(20);
	DECLARE v_sda_tj VARCHAR(20);
	DECLARE v_sda_drcs VARCHAR(20);
	DECLARE v_sda_dcyl VARCHAR(20);
	DECLARE v_nl INT;
	DECLARE v_condition_id VARCHAR(20);
	DECLARE v_count_type VARCHAR(20);
	DECLARE v_yl_unit VARCHAR(20);
	DECLARE v_xs VARCHAR(20);
	DECLARE v_PACKING_SPEC VARCHAR(20);
	DECLARE v_packing_uom VARCHAR(20);
	DECLARE v_count1 INT DEFAULT 0;
	DECLARE v_count2 INT DEFAULT 0;
	DECLARE v_count3 INT DEFAULT 0;
	DECLARE v_count4 INT DEFAULT 0;
	DECLARE v_count5 INT DEFAULT 0;
	DECLARE v_count6 INT DEFAULT 0;
	DECLARE v_count7 INT DEFAULT 0;
	DECLARE v_packing_min_spec VARCHAR(20);
	DECLARE v_yl_max VARCHAR(20);
	DECLARE v_ywa_name VARCHAR(50);
	DECLARE v_zx_flag VARCHAR(50);
	DECLARE v_dcsl DECIMAL(14,3);
	DECLARE v_gydw VARCHAR(20);
	DECLARE v_yl1 DECIMAL(14,3);
	
	-- 检查打粉情况
	SELECT COUNT(1) INTO v_count7
	FROM rms_t_pres_med 
	WHERE Code = p_Code AND his_code = p_yp_code 
	AND yysm LIKE '%打粉%';
	
	IF v_count7 > 0 THEN
			-- 打粉不分析用量
			LEAVE main_block;
	END IF;
	
	-- 检查中药执行标志
	SELECT COUNT(1) INTO v_count1
	FROM rms_itf_hos_drug 
	WHERE drug_code = p_yp_code AND ZX_FLAG = '3';
	
	-- 获取标准数据ID
	SELECT sda_id INTO v_sda_id
	FROM rms_t_byyydzb  
	WHERE akb020 = p_akb020 AND yp_code = p_yp_code
	LIMIT 1;
	
	-- 普通草药用量暂时不提示
	SELECT COUNT(1) INTO v_count6
	FROM rms_t_sda 
	WHERE ID = v_sda_id AND IFNULL(dx, '0') NOT IN ('1','2','3','4');
	
	IF v_count1 > 0 AND v_count6 > 0 THEN
			LEAVE main_block;
	END IF;
	
	-- 获取药品名称和执行标志
	SELECT DRUG_NAME, ZX_FLAG INTO v_ywa_name, v_zx_flag
	FROM rms_itf_hos_drug 
	WHERE DRUG_CODE = p_yp_code
	LIMIT 1;
	
	-- 处理给药途径
	SELECT COUNT(1) INTO v_count4
	FROM rms_t_tjdzb 
	WHERE h_tj = p_yp_tj;
	
	IF v_count4 > 1 THEN
			SELECT SUBSTRING(by_code, 1, 2) INTO v_sda_tj
			FROM rms_t_tjdzb 
			WHERE h_tj = p_yp_tj
			LIMIT 1;
	END IF;
	
	IF v_count4 = 1 THEN
			SELECT by_code INTO v_sda_tj
			FROM rms_t_tjdzb 
			WHERE h_tj = p_yp_tj
			LIMIT 1;
	END IF;
	
	-- 获取频次信息
	SELECT daily_times INTO v_sda_drcs
	FROM rms_itf_hos_frequency 
	WHERE freq_code = p_gypc
	LIMIT 1;
	
	-- 计算年龄（天数）
	SET v_nl = DATEDIFF(CURDATE(), p_csrq);
	
	-- 检查外用等情况
	SELECT COUNT(1) INTO v_count3
	FROM rms_t_pres_med 
	WHERE Code = p_Code AND his_code = p_yp_code 
	AND (yysm LIKE '%外%' OR yysm LIKE '%洗%' OR yysm LIKE '%浴%');
	
	-- 开始单位转换
	SET v_gydw = p_gydw;
	SET v_dcsl = CAST(p_dcsl AS DECIMAL(14,3));
	
	-- 单位标准化
	IF v_gydw = 'g' THEN
			SET v_gydw = '克';
	END IF;
	IF v_gydw = 'mg' THEN
			SET v_gydw = '毫克';
	END IF;
	IF v_gydw = 'ml' THEN
			SET v_gydw = '毫升';
	END IF;
	
	-- 获取包装规格信息
	SELECT PACKING_SPEC, packing_uom INTO v_PACKING_SPEC, v_packing_uom
	FROM rms_itf_hos_drug 
	WHERE DRUG_CODE = p_yp_code
	LIMIT 1;
	
	-- 获取条件ID和计数类型
	SELECT id, count_type INTO v_condition_id, v_count_type
	FROM rms_t_sda_cgl_condition
	WHERE sda_id = v_sda_id
	AND admin_routine LIKE CONCAT('%', v_sda_tj, '%')
	AND age_min < v_nl
	AND age_max > v_nl
	LIMIT 1;
	
	-- 获取用量单位
	SELECT yl_unit INTO v_yl_unit
	FROM rms_t_sda_cgl_result
	WHERE condition_id = v_condition_id
	AND reco_type != '2'
	LIMIT 1;
	
	-- 获取最小包装信息
	SELECT PACKING_MIN_QTY, packing_min_spec INTO v_xs, v_packing_min_spec
	FROM rms_itf_hos_drug
	WHERE drug_code = p_yp_code
	LIMIT 1;
	
	-- 单位转换逻辑
	IF v_gydw = '毫克' AND v_yl_unit = '克' THEN
			SET v_dcsl = v_dcsl / 1000;
			SET v_gydw = v_yl_unit;
	END IF;
	
	IF v_gydw = '克' AND v_yl_unit = '毫克' THEN
			SET v_dcsl = v_dcsl * 1000;
			SET v_gydw = v_yl_unit;
	END IF;
	
	IF v_gydw = 'u' AND v_yl_unit = '国际单位' THEN
			SET v_gydw = v_yl_unit;
	END IF;
	
	-- 复杂包装单位转换
	IF v_yl_unit = v_packing_uom AND v_gydw != v_yl_unit THEN
			IF v_packing_min_spec != v_yl_unit THEN
					IF v_packing_min_spec = 'mg' AND v_yl_unit = '克' THEN
							SET v_dcsl = (v_dcsl / CAST(v_xs AS DECIMAL)) / 1000;
							SET v_gydw = v_yl_unit;
					END IF;
					
					IF v_gydw = 'g' AND v_yl_unit = '毫克' THEN
							SET v_dcsl = (v_dcsl / CAST(v_xs AS DECIMAL)) * 1000;
							SET v_gydw = v_yl_unit;
					END IF;
			END IF;
			
			IF (v_packing_min_spec = v_yl_unit) OR 
				 (v_packing_min_spec = 'mg' AND v_yl_unit = '毫克') OR 
				 (v_packing_min_spec = 'g' AND v_yl_unit = '克') THEN
					SET v_dcsl = v_dcsl / CAST(v_xs AS DECIMAL);
					SET v_gydw = v_yl_unit;
			END IF;
	END IF;
	
	IF v_gydw = v_packing_uom AND v_gydw != v_yl_unit THEN
			IF v_packing_min_spec != v_yl_unit THEN
					IF v_packing_min_spec = 'mg' AND v_yl_unit = '克' THEN
							SET v_dcsl = v_dcsl * CAST(v_xs AS DECIMAL) / 1000;
							SET v_gydw = v_yl_unit;
					END IF;
					
					IF v_gydw = 'g' AND v_yl_unit = '毫克' THEN
							SET v_dcsl = v_dcsl * CAST(v_xs AS DECIMAL) * 1000;
							SET v_gydw = v_yl_unit;
					END IF;
			END IF;
			
			IF (v_packing_min_spec = v_yl_unit) OR 
				 (v_packing_min_spec = 'mg' AND v_yl_unit = '毫克') OR 
				 (v_packing_min_spec = 'g' AND v_yl_unit = '克') THEN
					SET v_dcsl = v_dcsl * CAST(v_xs AS DECIMAL);
					SET v_gydw = v_yl_unit;
			END IF;
	END IF;
	
	-- 单位转换结束，如果单位不匹配则退出
	IF v_gydw != v_yl_unit THEN
			LEAVE main_block;
	END IF;
	
	-- 中药内服检查
	IF v_count1 > 0 AND v_count3 = 0 THEN
			INSERT INTO rms_t_pres_fx (
					Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
			)
			SELECT 
					p_Code,
					v_ywa_name,
					'',
					'1',
					'一般提示',
					'RLT030',
					'YHGXHCGYFYLWT_PC',
					'药品用法用量',
					CONCAT(c.tymc, '用量不符合药品说明书推荐用量'),
					CONCAT('医院规定有毒草药内服最大量为药典推荐量：', c.tymc, '超过了最大用量', a.yl_max, a.yl_unit),
					0,
					'单次常规用量分析'
			FROM rms_t_sda c 
			LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
			WHERE c.id = v_sda_id
			AND a.reco_type = '1'
			AND a.yl_max < v_dcsl
			LIMIT 1;
	END IF;
	
	-- 中药外用检查
	IF v_count1 > 0 AND v_count3 > 0 THEN
			INSERT INTO rms_t_pres_fx (
					Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
			)
			SELECT 
					p_Code,
					v_ywa_name,
					'',
					'1',
					'一般提示',
					'RLT030',
					'YHGXHCGYFYLWT_PC',
					'药品用法用量',
					CONCAT(c.tymc, '用量不符合药品说明书推荐用量'),
					CONCAT('医院规定有毒草药外用最大量为药典推荐量：', c.tymc, '超过了最大用量', a.yl_max, a.yl_unit),
					0,
					'单次常规用量分析'
			FROM rms_t_sda c 
			LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
			WHERE c.id = v_sda_id
			AND a.reco_type = '1'
			AND a.yl_max < v_dcsl
			LIMIT 1;
	END IF;
	
	-- 单次超极量分析
	IF v_count_type = '0' AND v_count3 = 0 THEN
			-- 单次超极量分析
			INSERT INTO rms_t_pres_fx (
					Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
			)
			SELECT 
					p_Code,
					v_ywa_name,
					'',
					'1',
					'一般提示',
					'RLT030',
					'YHGXHCGYFYLWT_PC',
					'药品用法用量',
					CONCAT(c.tymc, '用量不符合药品说明书推荐用量'),
					CONCAT('说明书提示：', c.tymc, '常规用量为：', a.yl_min, '~', a.yl_max, a.yl_unit),
					0,
					'单次常规用量分析'
			FROM rms_t_sda c 
			LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
			WHERE c.ID = v_sda_id 
			AND a.condition_id = v_condition_id
			AND a.reco_type = '0'
			AND (a.yl_min > v_dcsl OR a.yl_max < v_dcsl)
			LIMIT 1;
			
			-- 单日量分析
			SELECT COUNT(1) INTO v_count5
			FROM rms_t_sda c 
			LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
			WHERE c.ID = v_sda_id 
			AND a.condition_id = v_condition_id
			AND a.reco_type = '0'
			AND (a.yl_min > v_dcsl OR a.yl_max < v_dcsl);
			
			IF v_count5 = 0 THEN
					SET v_yl1 = v_dcsl * CAST(v_sda_drcs AS DECIMAL);
					
					INSERT INTO rms_t_pres_fx (
							Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
					)
					SELECT 
							p_Code,
							v_ywa_name,
							'',
							'1',
							'一般提示',
							'RLT030',
							'YHGXHCGYFYLWT_PC',
							'药品用法用量',
							CONCAT(c.tymc, '单日用量不符合药品说明书推荐用量'),
							CONCAT('说明书提示：', c.tymc, '单日常规用量为：', a.yl_min, '~', a.yl_max, a.yl_unit),
							0,
							'单日常规用量分析'
					FROM rms_t_sda c 
					LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
					WHERE c.ID = v_sda_id 
					AND a.condition_id = v_condition_id
					AND a.reco_type = '1'
					AND (a.yl_min > v_yl1 OR a.yl_max < v_yl1)
					LIMIT 1;
			END IF;
	END IF;
	
	-- 按体重检查
	IF v_count_type = '1' AND v_count3 = 0 THEN
			IF p_tz = '' OR IFNULL(p_tz, '0') = '0' THEN
					LEAVE main_block;
			END IF;
			
			INSERT INTO rms_t_pres_fx (
					Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
			)
			SELECT 
					p_Code,
					v_ywa_name,
					'',
					'1',
					'一般提示',
					'RLT030',
					'YHGXHCGYFYLWT_PC',
					'药品用法用量',
					CONCAT(c.tymc, '用量不符合药品说明书推荐用量'),
					CONCAT('说明书提示：', c.tymc, '常规用量为每公斤：', a.yl_min, '~', a.yl_max, a.yl_unit),
					0,
					'单次常规用量分析'
			FROM rms_t_sda c 
			LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
			WHERE c.ID = v_sda_id 
			AND a.condition_id = v_condition_id
			AND a.reco_type = '0'
			AND (a.yl_min * CAST(p_tz AS DECIMAL) > v_dcsl OR a.yl_max * CAST(p_tz AS DECIMAL) < v_dcsl)
			LIMIT 1;
	END IF;

END