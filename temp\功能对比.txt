// MSSQL存储过程：
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- drop proc fx_cglfx


--fx_cgl '0b046704-fd4a-4a15-b9e3-75f0c3d93965','380','462270','','12.0000','g','01','1982-12-08',''
-- =============================================
ALTER PROCEDURE [dbo].[fx_cgl]
-- cglfx '123', '380','50','0202','3','ml','9','2019-07-13 00:00:00','20'
-- cglfx '123', '380','50','0202','3','ml','一天三次（TID）','20090209','20'
--cglfx '380','25','0101','7','mg','一天三次（TID）','20170209','30'
--cglfx '380','55','02','11','g','hs--临睡时','20170209','30'
---单次常规用量分析
@Code nvarchar(50),
@akb020  nvarchar(20),
@yp_code nvarchar(20),
@yp_tj    nvarchar(20),
@dcsl    nvarchar(20),
@gydw     nvarchar(20),
@gypc    nvarchar(20),
@csrq    smalldatetime,
@tz   nvarchar(20)----体重
AS

declare @sda_id nvarchar(20)
declare @sda_tj nvarchar(20)
declare @sda_drcs nvarchar(20)
declare @sda_dcyl nvarchar(20)
declare @nl nvarchar(20)
declare @condition_id nvarchar(20)
declare @count_type nvarchar(20)
declare @yl_unit nvarchar(20)
declare @xs nvarchar(20)
declare @PACKING_SPEC nvarchar(20)
declare @packing_uom nvarchar(20)
declare @count1 int
declare @count2 int
declare @count3 int
declare @count4 int
declare @count5 int
declare @count6 int
declare @count7 int
declare @packing_min_spec nvarchar(20)
declare @yl_max nvarchar(20)

-- select top 0 * from t_pres_fx

return
BEGIN
---打粉不分析用量
select @count7=COUNT(1) from t_pres_med 
where Code =@Code and his_code=@yp_code 
and yysm like '%打粉%'   ;
if @count7 >0
begin 
return
end

select @count1=count(1) from itf_hos_drug where drug_code =@yp_code and ZX_FLAG ='3'
select @sda_id=sda_id from t_byyydzb  where akb020 =@akb020 and yp_code=@yp_code;
----普通草药用量暂时不提示

select @count6=COUNT(1) from t_sda where ID =@sda_id and isnull(dx,'0') not in ('1','2','3','4')

if @count1 >0 and @count6 >0
begin 
return
end

declare @ywa_name nvarchar(50)
declare @zx_flag nvarchar(50)
select @ywa_name=DRUG_NAME,@zx_flag=ZX_FLAG from ITF_HOS_DRUG where DRUG_CODE =@yp_code
---草药暂时屏蔽
--if @zx_flag ='3'
--begin
--return
--end



select @count4=COUNT(1) from t_tjdzb where h_tj= @yp_tj;
if @count4 >1 
begin 
select top 1  @sda_tj= substring(by_code,1,2) from t_tjdzb where h_tj= @yp_tj;
end 
if @count4 =1 
begin 
select distinct @sda_tj= by_code from t_tjdzb where h_tj= @yp_tj;
end 
--select @sda_dcyl=minjl*cast(@dcsl as decimal) from T_HIS_DRUG where ypcode = @yp_code and unit=@gydw;
select @sda_drcs=daily_times from ITF_HOS_FREQUENCY where freq_code =@gypc ;
select  @nl=DATEDIFF(dd,@csrq,GETDATE()) ; 


----张扬20201110处理中药打粉外用等不分析用量
select @count3=COUNT(1) from t_pres_med 
where Code =@Code and his_code=@yp_code 
and (yysm like '%外%' or yysm like '%洗%' or yysm like'%浴%' ) ;




--select @condition_id='49133';


----select @count2=count(1) from t_tjdzb a,t_tj_base b where  a.by_code =b.dm and a.h_tj =@yp_tj and b.dm in ('C01','C02','C03','C04','C05','C06')



--if @count1 >0 
----and @count2 >0
--begin 
--select @Code, c.tymc ywa,'' ywb,'1'  wtlvlcode,
--  '一般提示'  wtlvl,
--  'RLT030'  wtcode,
--  'YHGXHCGYFYLWT_PC'  wtsp,
--  '药品用法用量' wtname,
--  CAST(c.tymc as varchar)+'++++' +'用量不符合药品说明书推荐用量'  title,
--  '说明书提示：'+ CAST(c.tymc as varchar)+'++++'+'常规用量为：' +cast(a.yl_min as varchar)+'~'+cast(a.yl_max as varchar)+a.yl_unit as detail
--,0,'单次常规用量分析' from t_sda c left join t_sda_cgl_result a on  a.sda_id =c.ID   where c.ID =@sda_id 
--and reco_type ='1'
--and (a.yl_min>@dcsl or a.yl_max <@dcsl)
--end 


--if @count1 =0 
--begin 

----单位转换开始

if @gydw='g'
set @gydw ='克'
if  @gydw='mg'
set @gydw ='毫克'
if @gydw='ml'
set @gydw ='毫升'



select @PACKING_SPEC=PACKING_SPEC,@packing_uom=packing_uom from ITF_HOS_DRUG where DRUG_CODE=@yp_code;

select @condition_id=id,@count_type=count_type from t_sda_cgl_condition
where sda_id =@sda_id
and admin_routine like '%'+@sda_tj+'%'
and age_min <@nl
and age_max >@nl;


select @yl_unit=yl_unit from t_sda_cgl_result
where condition_id =@condition_id
and  reco_type !='2';

select @xs=PACKING_MIN_QTY,@packing_min_spec=packing_min_spec from ITF_HOS_DRUG
where drug_code =@yp_code;



--if @gydw <>@yl_unit and ((@gydw ='ml' and @yl_unit ='毫升') or (@gydw ='mg' and @yl_unit ='毫克') or (@gydw ='g' and @yl_unit ='克') )
--begin 
--set @gydw =@yl_unit

--end 

--if @gydw <>@yl_unit and @yl_unit =@PACKING_SPEC
--begin 
--set @dcsl =cast(@dcsl as float)*@xs
--end 

if ( @gydw ='毫克' ) and @yl_unit ='克'
begin 
set @dcsl =cast(@dcsl as decimal)/1000
set @gydw =@yl_unit
end 

if (@gydw ='克' ) and @yl_unit ='毫克'
begin 
set @dcsl =cast(@dcsl as float)*1000
set @gydw =@yl_unit
end 

if @gydw ='u'   and @yl_unit ='国际单位'
begin
set @gydw =@yl_unit
end 

if @yl_unit =@packing_uom and @gydw <>@yl_unit
begin 
if @packing_min_spec <>@yl_unit
begin 

if ( @packing_min_spec ='mg' ) and @yl_unit ='克'
begin 
set @dcsl =(cast(@dcsl as decimal)/@xs)/1000
set @gydw =@yl_unit
end 

if (@gydw ='g' ) and @yl_unit ='毫克'
begin 
set @dcsl =(cast(@dcsl as float)/@xs)*1000
set @gydw =@yl_unit
end 
end
if ((@packing_min_spec =@yl_unit) or (@packing_min_spec='mg' and @yl_unit ='毫克') or (@packing_min_spec='g' and @yl_unit ='克'))
begin 
set @dcsl =cast(@dcsl as FLOAT)/@xs
set @gydw =@yl_unit
end
end 

if @gydw=@packing_uom and @gydw <>@yl_unit
begin 
if @packing_min_spec <>@yl_unit
begin 
if ( @packing_min_spec ='mg' ) and @yl_unit ='克'
begin 
set @dcsl =cast(@dcsl as decimal)*@xs/1000
set @gydw =@yl_unit
end 

if (@gydw ='g' ) and @yl_unit ='毫克'
begin 
set @dcsl =cast(@dcsl as float)*@xs*1000
set @gydw =@yl_unit
end 
end
if ((@packing_min_spec =@yl_unit )or (@packing_min_spec='mg' and @yl_unit ='毫克') or (@packing_min_spec='g' and @yl_unit ='克'))
begin 
set @dcsl =cast(@dcsl as FLOAT)*@xs
set @gydw =@yl_unit
end
end 
----单位转换结束

if @gydw !=@yl_unit
begin
return
end

---中药内服2倍用量 外服3倍用量
if @count1 >0 and @count3 =0
begin 
set @dcsl=cast(@dcsl as decimal(14,3))
select top 1 @Code, @ywa_name ywa,'' ywb,'1'  wtlvlcode,
  '一般提示'  wtlvl,
  'RLT030'  wtcode,
  'YHGXHCGYFYLWT_PC'  wtsp,
  '药品用法用量' wtname,
  CAST(c.tymc as varchar)+'++++' +'用量不符合药品说明书推荐用量'  title,
  '医院规定有毒草药内服最大量为药典推荐量：'+ CAST(c.tymc as varchar)+'++++'+'超过了最大用量' +cast(a.yl_max as varchar)+a.yl_unit as detail
,0,'单次常规用量分析' 
from t_sda c left join t_sda_cgl_result a on  a.sda_id =c.ID   where c.id =@sda_id
and a.reco_type ='1'
--and  a.yl_max <'100'
and  a.yl_max <@dcsl
end 

if @count1 >0 and @count3 >0
begin 
set @dcsl=cast(@dcsl as decimal(14,3))
select top 1 @Code, @ywa_name ywa,'' ywb,'1'  wtlvlcode,
  '一般提示'  wtlvl,
  'RLT030'  wtcode,
  'YHGXHCGYFYLWT_PC'  wtsp,
  '药品用法用量' wtname,
  CAST(c.tymc as varchar)+'++++' +'用量不符合药品说明书推荐用量'  title,
  '医院规定有毒草药外用最大量为药典推荐量：'+ CAST(c.tymc as varchar)+'++++'+'超过了最大用量' +cast(a.yl_max as varchar)+a.yl_unit as detail
,0,'单次常规用量分析' 
from t_sda c left join t_sda_cgl_result a on  a.sda_id =c.ID   where c.id =@sda_id
and a.reco_type ='1'
--and  a.yl_max <'100'
and  a.yl_max <@dcsl
end 





	if @count_type ='0' and @count3 =0
	begin
	----单次超极量分析
	select @Code, @ywa_name ywa,'' ywb,'1'  wtlvlcode,
	  '一般提示'  wtlvl,
	  'RLT030'  wtcode,
	  'YHGXHCGYFYLWT_PC'  wtsp,
	  '药品用法用量' wtname,
	  CAST(c.tymc as varchar)+'++++' +'用量不符合药品说明书推荐用量'  title,
	  '说明书提示：'+ CAST(c.tymc as varchar)+'++++'+'常规用量为：' +cast(a.yl_min as varchar)+'~'+cast(a.yl_max as varchar)+a.yl_unit as detail
	,0,'单次常规用量分析' from t_sda c left join t_sda_cgl_result a on  a.sda_id =c.ID   where c.ID =@sda_id and a.condition_id =@condition_id
	and reco_type ='0'
	and (a.yl_min>@dcsl or a.yl_max <@dcsl)
	---单日量分析

	select @count5=COUNT(1) from t_sda c left join t_sda_cgl_result a on  a.sda_id =c.ID   where c.ID =@sda_id and a.condition_id =@condition_id
	and reco_type ='0'
	and (a.yl_min>@dcsl or a.yl_max <@dcsl)

	if @count5 =0
	declare @yl1 nvarchar(20)
	set @yl1=@dcsl*cast(@sda_drcs as float)

	
	select @Code, @ywa_name ywa,'' ywb,'1'  wtlvlcode,
	  '一般提示'  wtlvl,
	  'RLT030'  wtcode,
	  'YHGXHCGYFYLWT_PC'  wtsp,
	  '药品用法用量' wtname,
	  CAST(c.tymc as varchar)+'++++' +'单日用量不符合药品说明书推荐用量'  title,
	  '说明书提示：'+ CAST(c.tymc as varchar)+'++++'+'单日常规用量为：' +cast(a.yl_min as varchar)+'~'+cast(a.yl_max as varchar)+a.yl_unit as detail
	,0,'单日常规用量分析' from t_sda c left join t_sda_cgl_result a on  a.sda_id =c.ID   where c.ID =@sda_id and a.condition_id =@condition_id
	and reco_type ='1'
	and (a.yl_min>@yl1 or a.yl_max <@yl1)


	end
	if @count_type='1' and @count3 =0


	begin 
	if @tz ='' or isnull(@tz ,'0')='0'  
	begin
	return
	end
	----按体重单次量
	select @Code,@ywa_name ywa,'' ywb,'1'  wtlvlcode,
	  '一般提示'  wtlvl,
	  'RLT030'  wtcode,
	  'YHGXHCGYFYLWT_PC'  wtsp,
	  '药品用法用量' wtname,
	  CAST(c.tymc as varchar)+'++++' +'用量不符合药品说明书推荐用量'  title,
	  '说明书提示：'+ CAST(c.tymc as varchar)+'++++'+'常规用量为每公斤：' +cast(a.yl_min as varchar)+'~'+cast(a.yl_max as varchar)+a.yl_unit as detail
	,0,'单次常规用量分析' from t_sda c left join t_sda_cgl_result a on  a.sda_id =c.ID   where c.ID =@sda_id and a.condition_id =@condition_id
	and reco_type ='0'
	and (a.yl_min*@tz>@dcsl or a.yl_max*@tz <@dcsl)
	end


END










// MySQL存储过程：
CREATE DEFINER=`root`@`%` PROCEDURE `rms_fx_cgl`(
    IN p_Code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_yp_tj VARCHAR(20),
    IN p_dcsl VARCHAR(20),
    IN p_gydw VARCHAR(20),
    IN p_gypc VARCHAR(20),
    IN p_csrq DATE,
    IN p_tz VARCHAR(20)
)
    COMMENT '单次常规用量分析存储过程'
main_block: BEGIN
	DECLARE v_sda_id VARCHAR(20);
	DECLARE v_sda_tj VARCHAR(20);
	DECLARE v_sda_drcs VARCHAR(20);
	DECLARE v_sda_dcyl VARCHAR(20);
	DECLARE v_nl INT;
	DECLARE v_condition_id VARCHAR(20);
	DECLARE v_count_type VARCHAR(20);
	DECLARE v_yl_unit VARCHAR(20);
	DECLARE v_xs VARCHAR(20);
	DECLARE v_PACKING_SPEC VARCHAR(20);
	DECLARE v_packing_uom VARCHAR(20);
	DECLARE v_count1 INT DEFAULT 0;
	DECLARE v_count2 INT DEFAULT 0;
	DECLARE v_count3 INT DEFAULT 0;
	DECLARE v_count4 INT DEFAULT 0;
	DECLARE v_count5 INT DEFAULT 0;
	DECLARE v_count6 INT DEFAULT 0;
	DECLARE v_count7 INT DEFAULT 0;
	DECLARE v_packing_min_spec VARCHAR(20);
	DECLARE v_yl_max VARCHAR(20);
	DECLARE v_ywa_name VARCHAR(50);
	DECLARE v_zx_flag VARCHAR(50);
	DECLARE v_dcsl DECIMAL(14,3);
	DECLARE v_gydw VARCHAR(20);
	DECLARE v_yl1 DECIMAL(14,3);
	
	-- 检查打粉情况
	SELECT COUNT(1) INTO v_count7
	FROM rms_t_pres_med 
	WHERE Code = p_Code AND his_code = p_yp_code 
	AND yysm LIKE '%打粉%';
	
	IF v_count7 > 0 THEN
			-- 打粉不分析用量
			LEAVE main_block;
	END IF;
	
	-- 检查中药执行标志
	SELECT COUNT(1) INTO v_count1
	FROM rms_itf_hos_drug 
	WHERE drug_code = p_yp_code AND ZX_FLAG = '3';
	
	-- 获取标准数据ID
	SELECT sda_id INTO v_sda_id
	FROM rms_t_byyydzb  
	WHERE akb020 = p_akb020 AND yp_code = p_yp_code
	LIMIT 1;
	
	-- 普通草药用量暂时不提示
	SELECT COUNT(1) INTO v_count6
	FROM rms_t_sda 
	WHERE ID = v_sda_id AND IFNULL(dx, '0') NOT IN ('1','2','3','4');
	
	IF v_count1 > 0 AND v_count6 > 0 THEN
			LEAVE main_block;
	END IF;
	
	-- 获取药品名称和执行标志
	SELECT DRUG_NAME, ZX_FLAG INTO v_ywa_name, v_zx_flag
	FROM rms_itf_hos_drug 
	WHERE DRUG_CODE = p_yp_code
	LIMIT 1;
	
	-- 处理给药途径
	SELECT COUNT(1) INTO v_count4
	FROM rms_t_tjdzb 
	WHERE h_tj = p_yp_tj;
	
	IF v_count4 > 1 THEN
			SELECT SUBSTRING(by_code, 1, 2) INTO v_sda_tj
			FROM rms_t_tjdzb 
			WHERE h_tj = p_yp_tj
			LIMIT 1;
	END IF;
	
	IF v_count4 = 1 THEN
			SELECT by_code INTO v_sda_tj
			FROM rms_t_tjdzb 
			WHERE h_tj = p_yp_tj
			LIMIT 1;
	END IF;
	
	-- 获取频次信息
	SELECT daily_times INTO v_sda_drcs
	FROM rms_itf_hos_frequency 
	WHERE freq_code = p_gypc
	LIMIT 1;
	
	-- 计算年龄（天数）
	SET v_nl = DATEDIFF(CURDATE(), p_csrq);
	
	-- 检查外用等情况
	SELECT COUNT(1) INTO v_count3
	FROM rms_t_pres_med 
	WHERE Code = p_Code AND his_code = p_yp_code 
	AND (yysm LIKE '%外%' OR yysm LIKE '%洗%' OR yysm LIKE '%浴%');
	
	-- 开始单位转换
	SET v_gydw = p_gydw;
	SET v_dcsl = CAST(p_dcsl AS DECIMAL(14,3));
	
	-- 单位标准化
	IF v_gydw = 'g' THEN
			SET v_gydw = '克';
	END IF;
	IF v_gydw = 'mg' THEN
			SET v_gydw = '毫克';
	END IF;
	IF v_gydw = 'ml' THEN
			SET v_gydw = '毫升';
	END IF;
	
	-- 获取包装规格信息
	SELECT PACKING_SPEC, packing_uom INTO v_PACKING_SPEC, v_packing_uom
	FROM rms_itf_hos_drug 
	WHERE DRUG_CODE = p_yp_code
	LIMIT 1;
	
	-- 获取条件ID和计数类型
	SELECT id, count_type INTO v_condition_id, v_count_type
	FROM rms_t_sda_cgl_condition
	WHERE sda_id = v_sda_id
	AND admin_routine LIKE CONCAT('%', v_sda_tj, '%')
	AND age_min < v_nl
	AND age_max > v_nl
	LIMIT 1;
	
	-- 获取用量单位
	SELECT yl_unit INTO v_yl_unit
	FROM rms_t_sda_cgl_result
	WHERE condition_id = v_condition_id
	AND reco_type != '2'
	LIMIT 1;
	
	-- 获取最小包装信息
	SELECT PACKING_MIN_QTY, packing_min_spec INTO v_xs, v_packing_min_spec
	FROM rms_itf_hos_drug
	WHERE drug_code = p_yp_code
	LIMIT 1;
	
	-- 单位转换逻辑
	IF v_gydw = '毫克' AND v_yl_unit = '克' THEN
			SET v_dcsl = v_dcsl / 1000;
			SET v_gydw = v_yl_unit;
	END IF;
	
	IF v_gydw = '克' AND v_yl_unit = '毫克' THEN
			SET v_dcsl = v_dcsl * 1000;
			SET v_gydw = v_yl_unit;
	END IF;
	
	IF v_gydw = 'u' AND v_yl_unit = '国际单位' THEN
			SET v_gydw = v_yl_unit;
	END IF;
	
	-- 复杂包装单位转换
	IF v_yl_unit = v_packing_uom AND v_gydw != v_yl_unit THEN
			IF v_packing_min_spec != v_yl_unit THEN
					IF v_packing_min_spec = 'mg' AND v_yl_unit = '克' THEN
							SET v_dcsl = (v_dcsl / CAST(v_xs AS DECIMAL)) / 1000;
							SET v_gydw = v_yl_unit;
					END IF;
					
					IF v_gydw = 'g' AND v_yl_unit = '毫克' THEN
							SET v_dcsl = (v_dcsl / CAST(v_xs AS DECIMAL)) * 1000;
							SET v_gydw = v_yl_unit;
					END IF;
			END IF;
			
			IF (v_packing_min_spec = v_yl_unit) OR 
				 (v_packing_min_spec = 'mg' AND v_yl_unit = '毫克') OR 
				 (v_packing_min_spec = 'g' AND v_yl_unit = '克') THEN
					SET v_dcsl = v_dcsl / CAST(v_xs AS DECIMAL);
					SET v_gydw = v_yl_unit;
			END IF;
	END IF;
	
	IF v_gydw = v_packing_uom AND v_gydw != v_yl_unit THEN
			IF v_packing_min_spec != v_yl_unit THEN
					IF v_packing_min_spec = 'mg' AND v_yl_unit = '克' THEN
							SET v_dcsl = v_dcsl * CAST(v_xs AS DECIMAL) / 1000;
							SET v_gydw = v_yl_unit;
					END IF;
					
					IF v_gydw = 'g' AND v_yl_unit = '毫克' THEN
							SET v_dcsl = v_dcsl * CAST(v_xs AS DECIMAL) * 1000;
							SET v_gydw = v_yl_unit;
					END IF;
			END IF;
			
			IF (v_packing_min_spec = v_yl_unit) OR 
				 (v_packing_min_spec = 'mg' AND v_yl_unit = '毫克') OR 
				 (v_packing_min_spec = 'g' AND v_yl_unit = '克') THEN
					SET v_dcsl = v_dcsl * CAST(v_xs AS DECIMAL);
					SET v_gydw = v_yl_unit;
			END IF;
	END IF;
	
	-- 单位转换结束，如果单位不匹配则退出
	IF v_gydw != v_yl_unit THEN
			LEAVE main_block;
	END IF;
	
	-- 中药内服检查
	IF v_count1 > 0 AND v_count3 = 0 THEN
			INSERT INTO rms_t_pres_fx (
					Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
			)
			SELECT 
					p_Code,
					v_ywa_name,
					'',
					'1',
					'一般提示',
					'RLT030',
					'YHGXHCGYFYLWT_PC',
					'药品用法用量',
					CONCAT(c.tymc, '用量不符合药品说明书推荐用量'),
					CONCAT('医院规定有毒草药内服最大量为药典推荐量：', c.tymc, '超过了最大用量', a.yl_max, a.yl_unit),
					0,
					'单次常规用量分析'
			FROM rms_t_sda c 
			LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
			WHERE c.id = v_sda_id
			AND a.reco_type = '1'
			AND a.yl_max < v_dcsl
			LIMIT 1;
	END IF;
	
	-- 中药外用检查
	IF v_count1 > 0 AND v_count3 > 0 THEN
			INSERT INTO rms_t_pres_fx (
					Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
			)
			SELECT 
					p_Code,
					v_ywa_name,
					'',
					'1',
					'一般提示',
					'RLT030',
					'YHGXHCGYFYLWT_PC',
					'药品用法用量',
					CONCAT(c.tymc, '用量不符合药品说明书推荐用量'),
					CONCAT('医院规定有毒草药外用最大量为药典推荐量：', c.tymc, '超过了最大用量', a.yl_max, a.yl_unit),
					0,
					'单次常规用量分析'
			FROM rms_t_sda c 
			LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
			WHERE c.id = v_sda_id
			AND a.reco_type = '1'
			AND a.yl_max < v_dcsl
			LIMIT 1;
	END IF;
	
	-- 单次超极量分析
	IF v_count_type = '0' AND v_count3 = 0 THEN
			-- 单次超极量分析
			INSERT INTO rms_t_pres_fx (
					Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
			)
			SELECT 
					p_Code,
					v_ywa_name,
					'',
					'1',
					'一般提示',
					'RLT030',
					'YHGXHCGYFYLWT_PC',
					'药品用法用量',
					CONCAT(c.tymc, '用量不符合药品说明书推荐用量'),
					CONCAT('说明书提示：', c.tymc, '常规用量为：', a.yl_min, '~', a.yl_max, a.yl_unit),
					0,
					'单次常规用量分析'
			FROM rms_t_sda c 
			LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
			WHERE c.ID = v_sda_id 
			AND a.condition_id = v_condition_id
			AND a.reco_type = '0'
			AND (a.yl_min > v_dcsl OR a.yl_max < v_dcsl)
			LIMIT 1;
			
			-- 单日量分析
			SELECT COUNT(1) INTO v_count5
			FROM rms_t_sda c 
			LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
			WHERE c.ID = v_sda_id 
			AND a.condition_id = v_condition_id
			AND a.reco_type = '0'
			AND (a.yl_min > v_dcsl OR a.yl_max < v_dcsl);
			
			IF v_count5 = 0 THEN
					SET v_yl1 = v_dcsl * CAST(v_sda_drcs AS DECIMAL);
					
					INSERT INTO rms_t_pres_fx (
							Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
					)
					SELECT 
							p_Code,
							v_ywa_name,
							'',
							'1',
							'一般提示',
							'RLT030',
							'YHGXHCGYFYLWT_PC',
							'药品用法用量',
							CONCAT(c.tymc, '单日用量不符合药品说明书推荐用量'),
							CONCAT('说明书提示：', c.tymc, '单日常规用量为：', a.yl_min, '~', a.yl_max, a.yl_unit),
							0,
							'单日常规用量分析'
					FROM rms_t_sda c 
					LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
					WHERE c.ID = v_sda_id 
					AND a.condition_id = v_condition_id
					AND a.reco_type = '1'
					AND (a.yl_min > v_yl1 OR a.yl_max < v_yl1)
					LIMIT 1;
			END IF;
	END IF;
	
	-- 按体重检查
	IF v_count_type = '1' AND v_count3 = 0 THEN
			IF p_tz = '' OR IFNULL(p_tz, '0') = '0' THEN
					LEAVE main_block;
			END IF;
			
			INSERT INTO rms_t_pres_fx (
					Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
			)
			SELECT 
					p_Code,
					v_ywa_name,
					'',
					'1',
					'一般提示',
					'RLT030',
					'YHGXHCGYFYLWT_PC',
					'药品用法用量',
					CONCAT(c.tymc, '用量不符合药品说明书推荐用量'),
					CONCAT('说明书提示：', c.tymc, '常规用量为每公斤：', a.yl_min, '~', a.yl_max, a.yl_unit),
					0,
					'单次常规用量分析'
			FROM rms_t_sda c 
			LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
			WHERE c.ID = v_sda_id 
			AND a.condition_id = v_condition_id
			AND a.reco_type = '0'
			AND (a.yl_min * CAST(p_tz AS DECIMAL) > v_dcsl OR a.yl_max * CAST(p_tz AS DECIMAL) < v_dcsl)
			LIMIT 1;
	END IF;

END